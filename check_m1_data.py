#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查M1数据和信号生成逻辑
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime

def check_m1_data():
    """检查M1数据和环比变化"""
    
    print("🔍 检查M1数据和环比变化...")
    
    try:
        # 获取M1数据
        money_supply_df = ak.macro_china_money_supply()
        
        # 数据清理
        money_supply_df['月份'] = money_supply_df['月份'].str.replace('年', '-').str.replace('月份', '').str.replace('月', '')
        money_supply_df['月份'] = pd.to_datetime(money_supply_df['月份'], format='%Y-%m')
        money_supply_df = money_supply_df.sort_values('月份')
        
        # 筛选需要的列
        m1_df = money_supply_df[['月份', '货币(M1)-数量(亿元)', '货币(M1)-同比增长']].copy()
        m1_df.columns = ['date', 'M1_amount', 'M1_growth']
        m1_df.set_index('date', inplace=True)
        
        # 计算环比变化
        m1_df['M1_growth_change'] = m1_df['M1_growth'].diff()
        
        # 生成信号
        m1_df['signal'] = np.where(m1_df['M1_growth_change'] >= 0, 1, 0)
        
        print(f"✅ 获取M1数据成功，共 {len(m1_df)} 条记录")
        print(f"📅 数据范围: {m1_df.index.min().strftime('%Y-%m')} 至 {m1_df.index.max().strftime('%Y-%m')}")
        
        # 重点检查2024年的数据
        print("\n" + "="*80)
        print("📊 2024年M1增长率及环比变化详情:")
        print("="*80)
        
        m1_2024 = m1_df[m1_df.index >= '2024-01-01'].copy()
        
        if len(m1_2024) > 0:
            print(f"{'月份':<12} {'M1增长率':<10} {'环比变化':<10} {'信号':<6} {'操作'}")
            print("-" * 60)
            
            for date, row in m1_2024.iterrows():
                month_str = date.strftime('%Y-%m')
                growth = row['M1_growth']
                change = row['M1_growth_change']
                signal = row['signal']
                action = "做多" if signal == 1 else "持现"
                
                change_str = f"{change:+.2f}" if not pd.isna(change) else "N/A"
                
                print(f"{month_str:<12} {growth:>8.2f}% {change_str:>9}% {signal:>4} {action}")
        
        # 统计2024年信号分布
        if len(m1_2024) > 0:
            signal_stats = m1_2024['signal'].value_counts()
            total_months = len(m1_2024)
            long_months = signal_stats.get(1, 0)
            cash_months = signal_stats.get(0, 0)
            
            print(f"\n📊 2024年信号统计:")
            print(f"   总月数: {total_months}")
            print(f"   做多月数: {long_months} ({long_months/total_months*100:.1f}%)")
            print(f"   持现月数: {cash_months} ({cash_months/total_months*100:.1f}%)")
            
            # 统计环比变化
            valid_changes = m1_2024['M1_growth_change'].dropna()
            if len(valid_changes) > 0:
                positive_changes = len(valid_changes[valid_changes >= 0])
                negative_changes = len(valid_changes[valid_changes < 0])
                
                print(f"\n📈 2024年M1增长率环比变化统计:")
                print(f"   环比上升或持平(≥0): {positive_changes} 次")
                print(f"   环比下降(<0): {negative_changes} 次")
        
        # 检查数据发布滞后逻辑
        print("\n" + "="*80)
        print("📅 数据发布滞后逻辑检查:")
        print("="*80)
        
        # 模拟发布时间表
        publish_data = []
        for date, row in m1_df.iterrows():
            data_month = date
            if data_month.month == 12:
                publish_date = pd.Timestamp(data_month.year + 1, 1, 15)
            else:
                publish_date = pd.Timestamp(data_month.year, data_month.month + 1, 15)
            
            publish_data.append({
                'data_month': data_month.strftime('%Y-%m'),
                'publish_date': publish_date.strftime('%Y-%m-%d'),
                'M1_growth': row['M1_growth'],
                'M1_growth_change': row['M1_growth_change'],
                'signal': row['signal']
            })
        
        publish_df = pd.DataFrame(publish_data)
        
        # 显示2024年的发布时间表
        publish_2024 = publish_df[publish_df['publish_date'] >= '2024-01-01'].head(15)
        
        print(f"{'数据月份':<10} {'发布日期':<12} {'M1增长率':<10} {'环比变化':<10} {'信号':<6}")
        print("-" * 70)
        
        for _, row in publish_2024.iterrows():
            change_str = f"{row['M1_growth_change']:+.2f}" if not pd.isna(row['M1_growth_change']) else "N/A"
            print(f"{row['data_month']:<10} {row['publish_date']:<12} {row['M1_growth']:>8.2f}% {change_str:>9}% {row['signal']:>4}")
        
        return m1_df
        
    except Exception as e:
        print(f"❌ 检查M1数据失败: {e}")
        return None

def main():
    print("🔍 M1数据和信号逻辑检查")
    print("=" * 60)
    
    m1_df = check_m1_data()
    
    if m1_df is not None:
        print("\n✅ 数据检查完成!")
        print("\n💡 如果2024年确实有很多环比下降但信号仍为做多，")
        print("   可能是以下原因之一：")
        print("   1. 数据发布滞后导致信号更新延迟")
        print("   2. 环比变化计算逻辑有误")
        print("   3. 信号生成条件设置问题")

if __name__ == "__main__":
    main()
