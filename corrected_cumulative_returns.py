#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的累积收益率图表
明确显示持现期间的效果
"""

import akshare as ak
import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
from datetime import datetime

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def get_data():
    """获取数据"""
    print("🌐 获取数据...")
    
    # 1. 获取M1数据
    money_supply_df = ak.macro_china_money_supply()
    money_supply_df['月份'] = money_supply_df['月份'].str.replace('年', '-').str.replace('月份', '').str.replace('月', '')
    money_supply_df['月份'] = pd.to_datetime(money_supply_df['月份'], format='%Y-%m')
    money_supply_df = money_supply_df.sort_values('月份')
    
    m1_df = money_supply_df[['月份', '货币(M1)-数量(亿元)', '货币(M1)-同比增长']].copy()
    m1_df.columns = ['date', 'M1_amount', 'M1_growth']
    m1_df.set_index('date', inplace=True)
    
    # 筛选2020年以后的数据
    m1_df = m1_df[m1_df.index >= '2020-01-01']
    
    # 2. 获取恒生指数数据
    hsi_ticker = yf.Ticker("^HSI")
    hsi_df = hsi_ticker.history(start='2020-01-01', end=datetime.now())
    hsi_df = hsi_df[['Close']].copy()
    hsi_df.columns = ['close']
    
    # 处理时区
    if hsi_df.index.tz is not None:
        hsi_df.index = hsi_df.index.tz_convert(None)
    hsi_df.index = pd.to_datetime(hsi_df.index)
    hsi_df = hsi_df.dropna()
    
    print(f"✅ M1数据: {len(m1_df)} 条记录")
    print(f"✅ 恒生指数数据: {len(hsi_df)} 条记录")
    
    return m1_df, hsi_df

def process_signals(m1_df, hsi_df):
    """处理信号和收益率计算"""
    print("🔄 处理信号和收益率...")
    
    # 1. 计算M1环比变化和信号
    m1_df['M1_growth_change'] = m1_df['M1_growth'].diff()
    m1_df['signal'] = np.where(m1_df['M1_growth_change'] >= 0, 1, 0)
    m1_df.loc[m1_df.index[0], 'signal'] = 1  # 第一个月设为做多
    
    # 2. 创建发布时间表（每月15号发布上月数据）
    publish_data = []
    for date, row in m1_df.iterrows():
        data_month = date
        if data_month.month == 12:
            publish_date = pd.Timestamp(data_month.year + 1, 1, 15)
        else:
            publish_date = pd.Timestamp(data_month.year, data_month.month + 1, 15)
        
        publish_data.append({
            'publish_date': publish_date,
            'data_month': data_month,
            'M1_growth': row['M1_growth'],
            'M1_growth_change': row['M1_growth_change'],
            'signal': row['signal']
        })
    
    publish_df = pd.DataFrame(publish_data)
    publish_df.set_index('publish_date', inplace=True)
    
    # 3. 合并数据
    daily_index = hsi_df.index
    m1_daily = publish_df.reindex(daily_index, method='ffill')
    
    combined_df = pd.merge(hsi_df, m1_daily, left_index=True, right_index=True, how='inner')
    combined_df = combined_df.dropna(subset=['M1_growth'])
    
    # 4. 计算收益率
    combined_df['hsi_return'] = combined_df['close'].pct_change().fillna(0)
    
    # 5. 计算策略收益率
    # 当信号=1时，获得恒生指数收益；当信号=0时，收益率为0（持现）
    combined_df['strategy_return'] = combined_df['signal'] * combined_df['hsi_return']
    
    # 6. 计算累积收益率
    combined_df['strategy_cumret'] = (1 + combined_df['strategy_return']).cumprod()
    combined_df['benchmark_cumret'] = (1 + combined_df['hsi_return']).cumprod()
    
    # 7. 计算持现期间的现金收益（假设年化1%的无风险收益）
    risk_free_daily = 0.01 / 252  # 年化1%转为日收益率
    combined_df['cash_return'] = np.where(combined_df['signal'] == 0, risk_free_daily, 0)
    combined_df['strategy_with_cash_return'] = combined_df['strategy_return'] + combined_df['cash_return']
    combined_df['strategy_with_cash_cumret'] = (1 + combined_df['strategy_with_cash_return']).cumprod()
    
    print(f"✅ 合并后数据: {len(combined_df)} 条记录")
    
    return combined_df

def plot_corrected_returns(df):
    """绘制修正的累积收益率图表"""
    print("📊 绘制修正的累积收益率图表...")
    
    # 重点关注2023-2025年的数据
    df_recent = df['2023-01-01':].copy()
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('M1环比变化择时策略 - 修正的累积收益率分析', fontsize=16, fontweight='bold')
    
    # 1. M1增长率及其变化
    ax1.plot(df_recent.index, df_recent['M1_growth'], 'b-', linewidth=2, label='M1增长率')
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df_recent.index, df_recent['M1_growth_change'], 'r-', linewidth=1.5, alpha=0.7, label='M1增长率变化')
    ax1_twin.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax1.set_title('M1增长率及其环比变化 (2023-2025)')
    ax1.set_ylabel('M1增长率(%)', color='blue')
    ax1_twin.set_ylabel('环比变化(%)', color='red')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 恒生指数价格
    ax2.plot(df_recent.index, df_recent['close'], 'g-', linewidth=1.5)
    ax2.set_title('恒生指数价格走势 (2023-2025)')
    ax2.set_ylabel('价格')
    ax2.grid(True, alpha=0.3)
    
    # 3. 累积收益率对比（重点图表）
    strategy_ret = (df_recent['strategy_cumret'] - 1) * 100
    benchmark_ret = (df_recent['benchmark_cumret'] - 1) * 100
    strategy_cash_ret = (df_recent['strategy_with_cash_cumret'] - 1) * 100
    
    ax3.plot(df_recent.index, strategy_ret, 'r-', linewidth=3, label='M1择时策略（持现无收益）', alpha=0.8)
    ax3.plot(df_recent.index, strategy_cash_ret, 'orange', linewidth=2, label='M1择时策略（持现1%年化）', alpha=0.8)
    ax3.plot(df_recent.index, benchmark_ret, 'b-', linewidth=2, label='买入持有', alpha=0.7)
    
    ax3.set_title('累积收益率对比 (2023-2025)', fontweight='bold', fontsize=14)
    ax3.set_ylabel('累积收益率(%)')
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    # 添加重要时间点标注
    important_dates = [
        ('2024-03-15', '开始持现'),
        ('2024-11-15', '重新做多'),
    ]
    
    for date_str, label in important_dates:
        date = pd.Timestamp(date_str)
        if date in df_recent.index:
            ax3.axvline(x=date, color='gray', linestyle=':', alpha=0.7)
            ax3.text(date, ax3.get_ylim()[1] * 0.9, label, rotation=90, 
                    verticalalignment='top', fontsize=9)
    
    # 4. 信号分布和持现期间效果
    # 绘制信号区域
    signal_periods = []
    current_signal = None
    start_date = None
    
    for date, signal in df_recent['signal'].items():
        if signal != current_signal:
            if current_signal is not None:
                signal_periods.append({
                    'start': start_date,
                    'end': date,
                    'signal': current_signal
                })
            current_signal = signal
            start_date = date
    
    # 添加最后一个周期
    if current_signal is not None:
        signal_periods.append({
            'start': start_date,
            'end': df_recent.index[-1],
            'signal': current_signal
        })
    
    # 绘制信号区域背景
    for period in signal_periods:
        color = 'lightgreen' if period['signal'] == 1 else 'lightcoral'
        ax4.axvspan(period['start'], period['end'], alpha=0.3, color=color)
    
    # 绘制策略vs基准的相对表现
    relative_performance = strategy_ret - benchmark_ret
    ax4.plot(df_recent.index, relative_performance, 'purple', linewidth=2, label='策略超额收益')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax4.set_title('策略相对表现 (策略收益 - 基准收益)')
    ax4.set_ylabel('超额收益率(%)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加图例说明
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='lightgreen', alpha=0.3, label='做多期'),
        Patch(facecolor='lightcoral', alpha=0.3, label='持现期')
    ]
    ax4.legend(handles=legend_elements, loc='upper left')
    
    plt.tight_layout()
    plt.savefig('corrected_cumulative_returns.png', dpi=300, bbox_inches='tight')
    print(f"📊 修正的累积收益率图表已保存到 corrected_cumulative_returns.png")
    plt.show()
    
    # 打印关键统计数据
    print_performance_stats(df_recent)

def print_performance_stats(df):
    """打印表现统计"""
    print("\n" + "="*80)
    print("📊 修正后的策略表现统计 (2023-2025)")
    print("="*80)
    
    # 计算年化收益率
    years = (df.index[-1] - df.index[0]).days / 365.25
    
    strategy_total = df['strategy_cumret'].iloc[-1] - 1
    benchmark_total = df['benchmark_cumret'].iloc[-1] - 1
    strategy_cash_total = df['strategy_with_cash_cumret'].iloc[-1] - 1
    
    strategy_annual = (df['strategy_cumret'].iloc[-1] ** (1/years)) - 1
    benchmark_annual = (df['benchmark_cumret'].iloc[-1] ** (1/years)) - 1
    strategy_cash_annual = (df['strategy_with_cash_cumret'].iloc[-1] ** (1/years)) - 1
    
    print(f"📅 分析期间: {df.index[0].strftime('%Y-%m-%d')} 至 {df.index[-1].strftime('%Y-%m-%d')} ({years:.1f}年)")
    print(f"\n💰 总收益率:")
    print(f"   M1择时策略（持现无收益）: {strategy_total:.2%}")
    print(f"   M1择时策略（持现1%年化）: {strategy_cash_total:.2%}")
    print(f"   买入持有基准:           {benchmark_total:.2%}")
    
    print(f"\n📊 年化收益率:")
    print(f"   M1择时策略（持现无收益）: {strategy_annual:.2%}")
    print(f"   M1择时策略（持现1%年化）: {strategy_cash_annual:.2%}")
    print(f"   买入持有基准:           {benchmark_annual:.2%}")
    
    # 信号统计
    signal_stats = df['signal'].value_counts()
    total_days = len(df)
    long_days = signal_stats.get(1, 0)
    cash_days = signal_stats.get(0, 0)
    
    print(f"\n📊 信号分布:")
    print(f"   总交易日: {total_days}")
    print(f"   做多天数: {long_days} ({long_days/total_days*100:.1f}%)")
    print(f"   持现天数: {cash_days} ({cash_days/total_days*100:.1f}%)")
    
    # 2024年特别分析
    df_2024 = df['2024-01-01':'2024-12-31']
    if len(df_2024) > 0:
        strategy_2024 = df_2024['strategy_cumret'].iloc[-1] / df_2024['strategy_cumret'].iloc[0] - 1
        benchmark_2024 = df_2024['benchmark_cumret'].iloc[-1] / df_2024['benchmark_cumret'].iloc[0] - 1
        
        print(f"\n🎯 2024年表现:")
        print(f"   M1择时策略: {strategy_2024:.2%}")
        print(f"   买入持有:   {benchmark_2024:.2%}")
        print(f"   超额收益:   {strategy_2024 - benchmark_2024:.2%}")

def main():
    print("🚀 修正的M1择时策略累积收益率分析")
    print("=" * 60)
    
    try:
        # 获取数据
        m1_df, hsi_df = get_data()
        
        # 处理信号和收益率
        combined_df = process_signals(m1_df, hsi_df)
        
        # 绘制修正的图表
        plot_corrected_returns(combined_df)
        
        print("\n✅ 修正的累积收益率分析完成!")
        print("\n💡 关键改进:")
        print("   1. 明确显示持现期间的效果")
        print("   2. 区分持现无收益 vs 持现获得无风险收益")
        print("   3. 突出显示2024年持现期间避险效果")
        print("   4. 显示策略相对基准的超额收益")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
