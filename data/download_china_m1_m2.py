#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国M1和M2货币供应量数据下载脚本
使用akshare获取东方财富的货币供应量数据
"""

import akshare as ak
import pandas as pd
import sqlite3
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def download_china_money_supply():
    """
    下载中国货币供应量数据（M0, M1, M2）
    数据来源：东方财富
    """
    try:
        print("🌐 正在从东方财富获取中国货币供应量数据...")
        
        # 获取货币供应量数据
        money_supply_df = ak.macro_china_money_supply()
        
        print(f"✅ 成功获取货币供应量数据，共 {len(money_supply_df)} 条记录")
        print("\n数据预览:")
        print(money_supply_df.head())
        
        # 数据清理和格式化
        # 处理日期格式，将"2025年06月份"转换为标准日期格式
        money_supply_df['月份'] = money_supply_df['月份'].str.replace('年', '-').str.replace('月份', '').str.replace('月', '')
        money_supply_df['月份'] = pd.to_datetime(money_supply_df['月份'], format='%Y-%m')
        money_supply_df = money_supply_df.sort_values('月份')
        
        # 保存到CSV文件
        csv_filename = f'china_money_supply_{datetime.now().strftime("%Y%m%d")}.csv'
        money_supply_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        print(f"💾 数据已保存到 {csv_filename}")
        
        # 保存到SQLite数据库
        db_filename = 'china_macro_data.db'
        with sqlite3.connect(db_filename) as conn:
            money_supply_df.to_sql('money_supply', conn, if_exists='replace', index=False)
            print(f"💾 数据已保存到数据库 {db_filename} 的 money_supply 表")
        
        return money_supply_df
        
    except Exception as e:
        print(f"❌ 获取货币供应量数据失败: {e}")
        return None

def download_china_m2_yearly():
    """
    下载中国M2年度数据
    数据来源：金十数据中心
    """
    try:
        print("\n🌐 正在从金十数据获取中国M2年度数据...")
        
        # 获取M2年度数据
        m2_yearly_df = ak.macro_china_m2_yearly()
        
        print(f"✅ 成功获取M2年度数据，共 {len(m2_yearly_df)} 条记录")
        print("\n数据预览:")
        print(m2_yearly_df.head())
        
        # 保存到CSV文件
        csv_filename = f'china_m2_yearly_{datetime.now().strftime("%Y%m%d")}.csv'
        m2_yearly_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        print(f"💾 M2年度数据已保存到 {csv_filename}")
        
        return m2_yearly_df
        
    except Exception as e:
        print(f"❌ 获取M2年度数据失败: {e}")
        return None

def plot_money_supply_data(money_supply_df):
    """
    绘制货币供应量数据图表
    """
    if money_supply_df is None or money_supply_df.empty:
        print("❌ 没有数据可供绘图")
        return
    
    try:
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('中国货币供应量数据分析', fontsize=16, fontweight='bold')
        
        # 获取最近5年的数据
        recent_data = money_supply_df.tail(60)  # 假设是月度数据，60个月约5年
        
        # 1. M2数量趋势
        ax1.plot(recent_data['月份'], recent_data['货币和准货币(M2)-数量(亿元)'], 
                color='blue', linewidth=2, marker='o', markersize=3)
        ax1.set_title('M2货币供应量趋势', fontweight='bold')
        ax1.set_ylabel('数量(亿元)')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. M1数量趋势
        ax2.plot(recent_data['月份'], recent_data['货币(M1)-数量(亿元)'], 
                color='green', linewidth=2, marker='s', markersize=3)
        ax2.set_title('M1货币供应量趋势', fontweight='bold')
        ax2.set_ylabel('数量(亿元)')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. M2同比增长率
        ax3.plot(recent_data['月份'], recent_data['货币和准货币(M2)-同比增长'], 
                color='red', linewidth=2, marker='^', markersize=3)
        ax3.set_title('M2同比增长率', fontweight='bold')
        ax3.set_ylabel('增长率(%)')
        ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. M1同比增长率
        ax4.plot(recent_data['月份'], recent_data['货币(M1)-同比增长'], 
                color='orange', linewidth=2, marker='d', markersize=3)
        ax4.set_title('M1同比增长率', fontweight='bold')
        ax4.set_ylabel('增长率(%)')
        ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax4.grid(True, alpha=0.3)
        ax4.tick_params(axis='x', rotation=45)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        chart_filename = f'china_money_supply_chart_{datetime.now().strftime("%Y%m%d")}.png'
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存到 {chart_filename}")
        
        # 显示图表
        plt.show()
        
    except Exception as e:
        print(f"❌ 绘制图表失败: {e}")

def analyze_money_supply_data(money_supply_df):
    """
    分析货币供应量数据
    """
    if money_supply_df is None or money_supply_df.empty:
        print("❌ 没有数据可供分析")
        return
    
    try:
        print("\n📊 货币供应量数据分析:")
        print("=" * 50)
        
        # 最新数据
        latest_data = money_supply_df.iloc[-1]
        print(f"📅 最新数据时间: {latest_data['月份'].strftime('%Y年%m月')}")
        print(f"💰 M2数量: {latest_data['货币和准货币(M2)-数量(亿元)']:,.0f} 亿元")
        print(f"💰 M1数量: {latest_data['货币(M1)-数量(亿元)']:,.0f} 亿元")
        print(f"💰 M0数量: {latest_data['流通中的现金(M0)-数量(亿元)']:,.0f} 亿元")
        
        print(f"\n📈 同比增长率:")
        print(f"   M2同比增长: {latest_data['货币和准货币(M2)-同比增长']:.2f}%")
        print(f"   M1同比增长: {latest_data['货币(M1)-同比增长']:.2f}%")
        print(f"   M0同比增长: {latest_data['流通中的现金(M0)-同比增长']:.2f}%")
        
        # 计算M1/M2比率
        m1_m2_ratio = latest_data['货币(M1)-数量(亿元)'] / latest_data['货币和准货币(M2)-数量(亿元)']
        print(f"\n📊 M1/M2比率: {m1_m2_ratio:.3f}")
        
        # 历史统计
        recent_12_months = money_supply_df.tail(12)
        print(f"\n📊 近12个月统计:")
        print(f"   M2平均增长率: {recent_12_months['货币和准货币(M2)-同比增长'].mean():.2f}%")
        print(f"   M1平均增长率: {recent_12_months['货币(M1)-同比增长'].mean():.2f}%")
        print(f"   M2增长率标准差: {recent_12_months['货币和准货币(M2)-同比增长'].std():.2f}%")
        print(f"   M1增长率标准差: {recent_12_months['货币(M1)-同比增长'].std():.2f}%")
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")

def main():
    """
    主函数
    """
    print("🚀 开始下载中国M1和M2货币供应量数据...")
    print("=" * 60)
    
    # 下载货币供应量数据
    money_supply_df = download_china_money_supply()
    
    # 下载M2年度数据
    m2_yearly_df = download_china_m2_yearly()
    
    if money_supply_df is not None:
        # 分析数据
        analyze_money_supply_data(money_supply_df)
        
        # 绘制图表
        plot_money_supply_data(money_supply_df)
    
    print("\n✅ 数据下载和分析完成!")

if __name__ == "__main__":
    main()
