#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试M1环比变化择时策略的收益率计算
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def test_return_calculation():
    """测试收益率计算逻辑"""
    
    # 创建模拟数据
    dates = pd.date_range('2020-01-01', '2020-12-31', freq='D')
    np.random.seed(42)
    
    # 模拟恒生指数价格（随机游走）
    returns = np.random.normal(0.0005, 0.02, len(dates))  # 日收益率
    prices = 25000 * (1 + returns).cumprod()
    
    # 模拟M1增长率（月度数据）
    monthly_dates = pd.date_range('2020-01-01', '2020-12-31', freq='MS')
    m1_growth = [8.5, 8.2, 7.8, 8.1, 8.6, 9.2, 8.9, 8.4, 7.9, 8.3, 8.7, 9.1]
    
    # 创建数据框
    df = pd.DataFrame({
        'date': dates,
        'close': prices,
        'hsi_return': returns
    })
    df.set_index('date', inplace=True)
    
    # 创建M1数据
    m1_df = pd.DataFrame({
        'date': monthly_dates,
        'M1_growth': m1_growth
    })
    m1_df.set_index('date', inplace=True)
    
    # 将M1数据扩展到日度（前向填充）
    m1_daily = m1_df.reindex(dates, method='ffill')
    
    # 合并数据
    df = pd.merge(df, m1_daily, left_index=True, right_index=True, how='left')
    df['M1_growth'] = df['M1_growth'].fillna(method='ffill')
    
    # 计算M1增长率环比变化
    df['M1_growth_change'] = df['M1_growth'].diff()
    
    # 生成信号：M1增长率环比上升时做多
    df['signal'] = np.where(df['M1_growth_change'] > 0, 1, 0)
    
    # 计算策略收益率
    df['strategy_return'] = df['signal'].shift(1) * df['hsi_return']
    df['strategy_return'] = df['strategy_return'].fillna(0)
    
    # 计算累计收益率
    df['strategy_cumret'] = (1 + df['strategy_return']).cumprod()
    df['benchmark_cumret'] = (1 + df['hsi_return']).cumprod()
    
    # 绘制结果
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. M1增长率及其变化
    ax1.plot(df.index, df['M1_growth'], 'b-', linewidth=2, label='M1增长率')
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['M1_growth_change'], 'r-', linewidth=1.5, alpha=0.7, label='M1增长率变化')
    ax1_twin.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax1.set_title('M1增长率及其环比变化')
    ax1.set_ylabel('M1增长率(%)', color='blue')
    ax1_twin.set_ylabel('环比变化(%)', color='red')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 恒生指数价格
    ax2.plot(df.index, df['close'], 'g-', linewidth=1.5)
    ax2.set_title('恒生指数价格')
    ax2.set_ylabel('价格')
    ax2.grid(True, alpha=0.3)
    
    # 3. 累计收益率对比
    ax3.plot(df.index, (df['strategy_cumret'] - 1) * 100, 'r-', linewidth=2, label='M1择时策略')
    ax3.plot(df.index, (df['benchmark_cumret'] - 1) * 100, 'b-', linewidth=2, label='买入持有')
    ax3.set_title('累计收益率对比')
    ax3.set_ylabel('累计收益率(%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 信号分布
    signal_changes = df[df['signal'].diff() != 0].copy()
    for i in range(len(signal_changes) - 1):
        start_date = signal_changes.index[i]
        end_date = signal_changes.index[i + 1]
        signal_value = signal_changes['signal'].iloc[i]
        color = 'lightgreen' if signal_value == 1 else 'lightcoral'
        ax4.axvspan(start_date, end_date, alpha=0.3, color=color)
    
    ax4.plot(df.index, df['M1_growth_change'], 'purple', linewidth=2)
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax4.set_title('择时信号分布')
    ax4.set_ylabel('M1增长率变化(%)')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_m1_strategy_returns.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print("📊 策略测试结果:")
    print(f"策略总收益: {(df['strategy_cumret'].iloc[-1] - 1) * 100:.2f}%")
    print(f"基准总收益: {(df['benchmark_cumret'].iloc[-1] - 1) * 100:.2f}%")
    print(f"做多天数: {df['signal'].sum()} / {len(df)} ({df['signal'].mean() * 100:.1f}%)")
    
    # 检查收益率计算是否连续
    strategy_returns = df['strategy_return'].dropna()
    print(f"\n收益率统计:")
    print(f"策略收益率范围: {strategy_returns.min():.4f} 到 {strategy_returns.max():.4f}")
    print(f"策略收益率标准差: {strategy_returns.std():.4f}")
    print(f"非零收益率天数: {(strategy_returns != 0).sum()}")
    
    return df

if __name__ == "__main__":
    print("🧪 测试M1环比变化择时策略收益率计算")
    print("=" * 50)
    
    test_df = test_return_calculation()
    
    print("\n✅ 测试完成！")
    print("如果累计收益率曲线是平滑的，说明计算逻辑正确。")
