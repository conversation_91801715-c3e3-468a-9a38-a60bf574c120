#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证日度信号是否正确
"""

import akshare as ak
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def verify_daily_signals():
    """验证日度信号传递是否正确"""
    
    print("🔍 验证日度信号传递...")
    
    try:
        # 1. 获取M1数据
        money_supply_df = ak.macro_china_money_supply()
        money_supply_df['月份'] = money_supply_df['月份'].str.replace('年', '-').str.replace('月份', '').str.replace('月', '')
        money_supply_df['月份'] = pd.to_datetime(money_supply_df['月份'], format='%Y-%m')
        money_supply_df = money_supply_df.sort_values('月份')
        
        m1_df = money_supply_df[['月份', '货币(M1)-数量(亿元)', '货币(M1)-同比增长']].copy()
        m1_df.columns = ['date', 'M1_amount', 'M1_growth']
        m1_df.set_index('date', inplace=True)
        
        # 计算环比变化和信号
        m1_df['M1_growth_change'] = m1_df['M1_growth'].diff()
        m1_df['signal'] = np.where(m1_df['M1_growth_change'] >= 0, 1, 0)
        m1_df['signal'].iloc[0] = 1  # 第一个月设为做多
        
        # 2. 创建发布时间表
        publish_data = []
        for date, row in m1_df.iterrows():
            data_month = date
            if data_month.month == 12:
                publish_date = pd.Timestamp(data_month.year + 1, 1, 15)
            else:
                publish_date = pd.Timestamp(data_month.year, data_month.month + 1, 15)
            
            publish_data.append({
                'publish_date': publish_date,
                'data_month': data_month,
                'M1_growth': row['M1_growth'],
                'M1_growth_change': row['M1_growth_change'],
                'signal': row['signal']
            })
        
        publish_df = pd.DataFrame(publish_data)
        publish_df.set_index('publish_date', inplace=True)
        
        # 3. 创建2024年的日度数据范围
        daily_dates = pd.date_range('2024-01-01', '2024-12-31', freq='D')
        
        # 4. 将发布数据扩展到日度
        daily_signals = publish_df.reindex(daily_dates, method='ffill')
        
        # 5. 检查关键时间点的信号
        print("\n📊 2024年关键时间点的日度信号检查:")
        print("="*70)
        
        key_dates = [
            '2024-01-15',  # 2023年12月数据发布
            '2024-02-15',  # 2024年1月数据发布
            '2024-03-15',  # 2024年2月数据发布
            '2024-04-15',  # 2024年3月数据发布
            '2024-05-15',  # 2024年4月数据发布
            '2024-06-15',  # 2024年5月数据发布
            '2024-07-15',  # 2024年6月数据发布
            '2024-08-15',  # 2024年7月数据发布
            '2024-09-15',  # 2024年8月数据发布
            '2024-10-15',  # 2024年9月数据发布
            '2024-11-15',  # 2024年10月数据发布
            '2024-12-15',  # 2024年11月数据发布
        ]
        
        print(f"{'发布日期':<12} {'数据月份':<10} {'M1增长率':<10} {'环比变化':<10} {'信号':<6} {'操作'}")
        print("-" * 70)
        
        for date_str in key_dates:
            date = pd.Timestamp(date_str)
            if date in daily_signals.index:
                row = daily_signals.loc[date]
                data_month = row['data_month'].strftime('%Y-%m') if pd.notna(row['data_month']) else 'N/A'
                growth = row['M1_growth'] if pd.notna(row['M1_growth']) else 0
                change = row['M1_growth_change'] if pd.notna(row['M1_growth_change']) else 0
                signal = row['signal'] if pd.notna(row['signal']) else 0
                action = "做多" if signal == 1 else "持现"
                
                change_str = f"{change:+.2f}" if change != 0 else "0.00"
                print(f"{date_str:<12} {data_month:<10} {growth:>8.2f}% {change_str:>9}% {signal:>4.0f} {action}")
        
        # 6. 统计2024年每个月的信号分布
        print(f"\n📊 2024年每月信号分布:")
        print("="*50)
        
        monthly_stats = []
        for month in range(1, 13):
            month_start = pd.Timestamp(2024, month, 1)
            if month == 12:
                month_end = pd.Timestamp(2024, 12, 31)
            else:
                month_end = pd.Timestamp(2024, month + 1, 1) - pd.Timedelta(days=1)
            
            month_data = daily_signals[month_start:month_end]
            if len(month_data) > 0:
                avg_signal = month_data['signal'].mean()
                dominant_signal = 1 if avg_signal > 0.5 else 0
                dominant_action = "做多" if dominant_signal == 1 else "持现"
                
                monthly_stats.append({
                    'month': f"2024-{month:02d}",
                    'avg_signal': avg_signal,
                    'dominant_action': dominant_action
                })
                
                print(f"{f'2024-{month:02d}':<10} 平均信号: {avg_signal:.2f} → {dominant_action}")
        
        # 7. 绘制信号图
        plot_signals(daily_signals)
        
        return daily_signals
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return None

def plot_signals(daily_signals):
    """绘制信号图"""
    
    # 筛选2024年数据
    signals_2024 = daily_signals['2024-01-01':'2024-12-31'].copy()
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    fig.suptitle('2024年M1择时信号验证', fontsize=16, fontweight='bold')
    
    # 1. M1增长率及其变化
    ax1.plot(signals_2024.index, signals_2024['M1_growth'], 'b-', linewidth=2, label='M1增长率')
    ax1_twin = ax1.twinx()
    ax1_twin.plot(signals_2024.index, signals_2024['M1_growth_change'], 'r-', linewidth=1.5, alpha=0.7, label='M1增长率变化')
    ax1_twin.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax1.set_title('M1增长率及其环比变化')
    ax1.set_ylabel('M1增长率(%)', color='blue')
    ax1_twin.set_ylabel('环比变化(%)', color='red')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 信号分布
    # 绘制信号区域
    signal_periods = []
    current_signal = None
    start_date = None
    
    for date, signal in signals_2024['signal'].items():
        if signal != current_signal:
            if current_signal is not None:
                signal_periods.append({
                    'start': start_date,
                    'end': date,
                    'signal': current_signal
                })
            current_signal = signal
            start_date = date
    
    # 添加最后一个周期
    if current_signal is not None:
        signal_periods.append({
            'start': start_date,
            'end': signals_2024.index[-1],
            'signal': current_signal
        })
    
    # 绘制信号区域
    for period in signal_periods:
        color = 'lightgreen' if period['signal'] == 1 else 'lightcoral'
        ax2.axvspan(period['start'], period['end'], alpha=0.3, color=color)
    
    ax2.plot(signals_2024.index, signals_2024['signal'], 'k-', linewidth=2, label='择时信号')
    ax2.set_title('择时信号分布（1=做多，0=持现）')
    ax2.set_ylabel('信号值')
    ax2.set_ylim(-0.1, 1.1)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='lightgreen', alpha=0.3, label='做多期'),
                      Patch(facecolor='lightcoral', alpha=0.3, label='持现期')]
    ax2.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.savefig('daily_signals_verification_2024.png', dpi=300, bbox_inches='tight')
    print(f"\n📊 信号验证图表已保存到 daily_signals_verification_2024.png")
    plt.show()

def main():
    print("🔍 验证M1择时策略的日度信号传递")
    print("=" * 60)
    
    daily_signals = verify_daily_signals()
    
    if daily_signals is not None:
        print("\n✅ 验证完成!")
        print("\n💡 从结果可以看出：")
        print("   - 2024年2-9月确实是持现期（信号=0）")
        print("   - 2024年10-12月是做多期（信号=1）")
        print("   - 信号传递到日度数据是正确的")

if __name__ == "__main__":
    main()
