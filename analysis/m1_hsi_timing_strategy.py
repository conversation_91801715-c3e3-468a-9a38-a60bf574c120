#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国M1增速对恒生指数择时策略分析
使用中国M1货币供应量同比增长率作为择时信号来交易恒生指数
"""

import akshare as ak
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams
import sqlite3
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

class M1HSITimingStrategy:
    def __init__(self, start_date='2015-01-01'):
        self.start_date = start_date
        self.m1_data = None
        self.hsi_data = None
        self.combined_data = None
        self.strategy_results = None
        
    def get_china_m1_data(self):
        """获取中国M1货币供应量数据"""
        print("🌐 正在获取中国M1货币供应量数据...")
        
        try:
            # 获取货币供应量数据
            money_supply_df = ak.macro_china_money_supply()
            
            if money_supply_df.empty:
                print("❌ 未获取到M1数据")
                return False
            
            # 数据清理和格式化
            money_supply_df['月份'] = money_supply_df['月份'].str.replace('年', '-').str.replace('月份', '').str.replace('月', '')
            money_supply_df['月份'] = pd.to_datetime(money_supply_df['月份'], format='%Y-%m')
            money_supply_df = money_supply_df.sort_values('月份')
            
            # 筛选需要的列
            m1_df = money_supply_df[['月份', '货币(M1)-数量(亿元)', '货币(M1)-同比增长']].copy()
            m1_df.columns = ['date', 'M1_amount', 'M1_growth']
            m1_df.set_index('date', inplace=True)
            
            # 筛选时间范围
            start_dt = pd.to_datetime(self.start_date)
            m1_df = m1_df[m1_df.index >= start_dt]
            
            self.m1_data = m1_df
            print(f"✅ 成功获取M1数据，共 {len(m1_df)} 条记录")
            print(f"📅 数据范围: {m1_df.index.min().strftime('%Y-%m')} 至 {m1_df.index.max().strftime('%Y-%m')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取M1数据失败: {e}")
            return False
    
    def get_hsi_data(self):
        """获取恒生指数数据"""
        print("🌐 正在获取恒生指数数据...")
        
        try:
            # 使用akshare获取恒生指数数据
            hsi_df = ak.stock_hk_index_daily_em(symbol="HSI")
            
            if hsi_df.empty:
                print("❌ 未获取到恒生指数数据")
                return False
            
            # 数据清理
            if '日期' in hsi_df.columns:
                hsi_df['date'] = pd.to_datetime(hsi_df['日期'])
                hsi_df['close'] = pd.to_numeric(hsi_df['收盘'], errors='coerce')
            elif 'latest' in hsi_df.columns:
                hsi_df['date'] = pd.to_datetime(hsi_df['date'])
                hsi_df['close'] = pd.to_numeric(hsi_df['latest'], errors='coerce')
            else:
                hsi_df['date'] = pd.to_datetime(hsi_df['date'])
                hsi_df['close'] = pd.to_numeric(hsi_df['close'], errors='coerce')
            
            hsi_df.set_index('date', inplace=True)
            hsi_df = hsi_df[['close']].dropna()
            
            # 筛选时间范围
            start_dt = pd.to_datetime(self.start_date)
            hsi_df = hsi_df[hsi_df.index >= start_dt]
            
            self.hsi_data = hsi_df
            print(f"✅ 成功获取恒生指数数据，共 {len(hsi_df)} 条记录")
            print(f"📅 数据范围: {hsi_df.index.min().strftime('%Y-%m-%d')} 至 {hsi_df.index.max().strftime('%Y-%m-%d')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取恒生指数数据失败: {e}")
            return False
    
    def combine_data(self):
        """合并M1和恒生指数数据"""
        if self.m1_data is None or self.hsi_data is None:
            print("❌ 请先获取M1和恒生指数数据")
            return False
        
        print("🔄 正在合并数据...")
        
        try:
            # 将M1月度数据扩展到日度数据（前向填充）
            daily_index = pd.date_range(start=self.hsi_data.index.min(), 
                                      end=self.hsi_data.index.max(), 
                                      freq='D')
            
            # 重新索引M1数据到日度
            m1_daily = self.m1_data.reindex(daily_index, method='ffill')
            
            # 合并数据
            combined_df = pd.merge(self.hsi_data, m1_daily, 
                                 left_index=True, right_index=True, how='inner')
            
            # 计算恒生指数收益率
            combined_df['hsi_return'] = combined_df['close'].pct_change()
            combined_df['hsi_cumret'] = (1 + combined_df['hsi_return']).cumprod()
            
            # 删除缺失值
            combined_df = combined_df.dropna()
            
            self.combined_data = combined_df
            print(f"✅ 数据合并完成，共 {len(combined_df)} 条记录")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据合并失败: {e}")
            return False
    
    def generate_signals(self, m1_threshold=8.0):
        """
        生成择时信号
        当M1同比增长率 > threshold时，做多恒生指数
        当M1同比增长率 <= threshold时，持有现金
        """
        if self.combined_data is None:
            print("❌ 请先合并数据")
            return False
        
        print(f"🎯 生成择时信号 (M1增长率阈值: {m1_threshold}%)...")
        
        try:
            df = self.combined_data.copy()
            
            # 生成信号
            df['signal'] = np.where(df['M1_growth'] > m1_threshold, 1, 0)  # 1=做多, 0=持有现金
            df['signal_change'] = df['signal'].diff()
            
            # 计算策略收益
            df['strategy_return'] = df['signal'].shift(1) * df['hsi_return']  # 使用前一日信号
            df['strategy_cumret'] = (1 + df['strategy_return']).cumprod()
            
            # 计算基准收益（买入持有）
            df['benchmark_cumret'] = df['hsi_cumret']
            
            self.combined_data = df
            
            # 统计信号
            signal_stats = df['signal'].value_counts()
            total_days = len(df)
            long_days = signal_stats.get(1, 0)
            cash_days = signal_stats.get(0, 0)
            
            print(f"📊 信号统计:")
            print(f"   总交易日: {total_days}")
            print(f"   做多天数: {long_days} ({long_days/total_days*100:.1f}%)")
            print(f"   持现天数: {cash_days} ({cash_days/total_days*100:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 信号生成失败: {e}")
            return False
    
    def calculate_performance(self):
        """计算策略表现"""
        if self.combined_data is None:
            print("❌ 请先生成信号")
            return None
        
        print("📊 计算策略表现...")
        
        try:
            df = self.combined_data.dropna()
            
            # 策略表现
            strategy_total_return = df['strategy_cumret'].iloc[-1] - 1
            benchmark_total_return = df['benchmark_cumret'].iloc[-1] - 1
            
            # 年化收益率
            years = (df.index[-1] - df.index[0]).days / 365.25
            strategy_annual_return = (df['strategy_cumret'].iloc[-1] ** (1/years)) - 1
            benchmark_annual_return = (df['benchmark_cumret'].iloc[-1] ** (1/years)) - 1
            
            # 波动率
            strategy_volatility = df['strategy_return'].std() * np.sqrt(252)
            benchmark_volatility = df['hsi_return'].std() * np.sqrt(252)
            
            # 夏普比率（假设无风险利率为0）
            strategy_sharpe = strategy_annual_return / strategy_volatility if strategy_volatility > 0 else 0
            benchmark_sharpe = benchmark_annual_return / benchmark_volatility if benchmark_volatility > 0 else 0
            
            # 最大回撤
            strategy_peak = df['strategy_cumret'].expanding().max()
            strategy_drawdown = (df['strategy_cumret'] - strategy_peak) / strategy_peak
            strategy_max_drawdown = strategy_drawdown.min()
            
            benchmark_peak = df['benchmark_cumret'].expanding().max()
            benchmark_drawdown = (df['benchmark_cumret'] - benchmark_peak) / benchmark_peak
            benchmark_max_drawdown = benchmark_drawdown.min()
            
            # 胜率
            positive_returns = df[df['strategy_return'] > 0]
            win_rate = len(positive_returns) / len(df[df['strategy_return'] != 0]) if len(df[df['strategy_return'] != 0]) > 0 else 0
            
            results = {
                'strategy_total_return': strategy_total_return,
                'benchmark_total_return': benchmark_total_return,
                'strategy_annual_return': strategy_annual_return,
                'benchmark_annual_return': benchmark_annual_return,
                'strategy_volatility': strategy_volatility,
                'benchmark_volatility': benchmark_volatility,
                'strategy_sharpe': strategy_sharpe,
                'benchmark_sharpe': benchmark_sharpe,
                'strategy_max_drawdown': strategy_max_drawdown,
                'benchmark_max_drawdown': benchmark_max_drawdown,
                'win_rate': win_rate,
                'years': years
            }
            
            self.strategy_results = results
            
            # 打印结果
            print("\n" + "="*60)
            print("📈 策略表现分析")
            print("="*60)
            print(f"📅 回测期间: {df.index[0].strftime('%Y-%m-%d')} 至 {df.index[-1].strftime('%Y-%m-%d')} ({years:.1f}年)")
            print(f"\n💰 收益表现:")
            print(f"   M1择时策略总收益: {strategy_total_return:.2%}")
            print(f"   买入持有总收益:   {benchmark_total_return:.2%}")
            print(f"   超额收益:         {strategy_total_return - benchmark_total_return:.2%}")
            print(f"\n📊 年化指标:")
            print(f"   M1择时策略年化收益: {strategy_annual_return:.2%}")
            print(f"   买入持有年化收益:   {benchmark_annual_return:.2%}")
            print(f"   M1择时策略波动率:   {strategy_volatility:.2%}")
            print(f"   买入持有波动率:     {benchmark_volatility:.2%}")
            print(f"   M1择时策略夏普比率: {strategy_sharpe:.3f}")
            print(f"   买入持有夏普比率:   {benchmark_sharpe:.3f}")
            print(f"\n⚠️  风险指标:")
            print(f"   M1择时策略最大回撤: {strategy_max_drawdown:.2%}")
            print(f"   买入持有最大回撤:   {benchmark_max_drawdown:.2%}")
            print(f"   策略胜率:           {win_rate:.2%}")
            
            return results

        except Exception as e:
            print(f"❌ 表现计算失败: {e}")
            return None

    def plot_results(self):
        """绘制策略结果图表"""
        if self.combined_data is None:
            print("❌ 没有数据可供绘图")
            return

        try:
            df = self.combined_data.dropna()

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('M1增速恒生指数择时策略分析', fontsize=16, fontweight='bold')

            # 1. M1增长率趋势
            ax1.plot(df.index, df['M1_growth'], color='blue', linewidth=2, label='M1同比增长率')
            ax1.axhline(y=8.0, color='red', linestyle='--', alpha=0.7, label='阈值(8%)')
            ax1.set_title('中国M1货币供应量同比增长率', fontweight='bold')
            ax1.set_ylabel('增长率(%)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. 恒生指数价格
            ax2.plot(df.index, df['close'], color='green', linewidth=1.5, label='恒生指数')
            ax2.set_title('恒生指数价格走势', fontweight='bold')
            ax2.set_ylabel('指数点位')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 3. 累计收益对比
            ax3.plot(df.index, (df['strategy_cumret'] - 1) * 100,
                    color='red', linewidth=2, label='M1择时策略')
            ax3.plot(df.index, (df['benchmark_cumret'] - 1) * 100,
                    color='blue', linewidth=2, label='买入持有')
            ax3.set_title('累计收益率对比', fontweight='bold')
            ax3.set_ylabel('累计收益率(%)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 4. 信号分布
            signal_periods = []
            current_signal = None
            start_date = None

            for date, signal in df['signal'].items():
                if signal != current_signal:
                    if current_signal is not None:
                        signal_periods.append({
                            'start': start_date,
                            'end': date,
                            'signal': current_signal
                        })
                    current_signal = signal
                    start_date = date

            # 添加最后一个周期
            if current_signal is not None:
                signal_periods.append({
                    'start': start_date,
                    'end': df.index[-1],
                    'signal': current_signal
                })

            # 绘制信号区域
            for period in signal_periods:
                color = 'lightgreen' if period['signal'] == 1 else 'lightcoral'
                label = '做多期' if period['signal'] == 1 else '持现期'
                ax4.axvspan(period['start'], period['end'], alpha=0.3, color=color)

            ax4.plot(df.index, df['M1_growth'], color='blue', linewidth=2)
            ax4.axhline(y=8.0, color='red', linestyle='--', alpha=0.7)
            ax4.set_title('择时信号分布', fontweight='bold')
            ax4.set_ylabel('M1增长率(%)')

            # 添加图例
            from matplotlib.patches import Patch
            legend_elements = [Patch(facecolor='lightgreen', alpha=0.3, label='做多期'),
                             Patch(facecolor='lightcoral', alpha=0.3, label='持现期')]
            ax4.legend(handles=legend_elements, loc='upper right')
            ax4.grid(True, alpha=0.3)

            # 调整布局
            plt.tight_layout()

            # 保存图表
            chart_filename = f'm1_hsi_timing_strategy_{datetime.now().strftime("%Y%m%d")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存到 {chart_filename}")

            # 显示图表
            plt.show()

        except Exception as e:
            print(f"❌ 绘制图表失败: {e}")

    def save_results(self):
        """保存结果到文件"""
        if self.combined_data is None or self.strategy_results is None:
            print("❌ 没有结果可供保存")
            return

        try:
            # 保存详细数据到CSV
            csv_filename = f'm1_hsi_timing_data_{datetime.now().strftime("%Y%m%d")}.csv'
            self.combined_data.to_csv(csv_filename, encoding='utf-8-sig')
            print(f"💾 详细数据已保存到 {csv_filename}")

            # 保存结果到SQLite数据库
            db_filename = 'timing_strategy_results.db'
            with sqlite3.connect(db_filename) as conn:
                # 保存策略数据
                self.combined_data.reset_index().to_sql('m1_hsi_timing', conn,
                                                       if_exists='replace', index=False)

                # 保存策略表现
                results_df = pd.DataFrame([self.strategy_results])
                results_df['strategy_name'] = 'M1_HSI_Timing'
                results_df['update_time'] = datetime.now()
                results_df.to_sql('strategy_performance', conn,
                                if_exists='append', index=False)

            print(f"💾 结果已保存到数据库 {db_filename}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    print("🚀 M1增速恒生指数择时策略分析")
    print("=" * 60)

    # 创建策略实例
    strategy = M1HSITimingStrategy(start_date='2015-01-01')

    try:
        # 1. 获取M1数据
        if not strategy.get_china_m1_data():
            return

        # 2. 获取恒生指数数据
        if not strategy.get_hsi_data():
            return

        # 3. 合并数据
        if not strategy.combine_data():
            return

        # 4. 生成择时信号
        if not strategy.generate_signals(m1_threshold=8.0):
            return

        # 5. 计算策略表现
        results = strategy.calculate_performance()
        if results is None:
            return

        # 6. 绘制结果图表
        strategy.plot_results()

        # 7. 保存结果
        strategy.save_results()

        print("\n✅ M1择时策略分析完成!")

        # 8. 测试不同阈值
        print("\n🔍 测试不同M1增长率阈值的效果:")
        print("-" * 40)
        thresholds = [6.0, 7.0, 8.0, 9.0, 10.0]

        for threshold in thresholds:
            test_strategy = M1HSITimingStrategy(start_date='2015-01-01')
            test_strategy.m1_data = strategy.m1_data
            test_strategy.hsi_data = strategy.hsi_data
            test_strategy.combine_data()
            test_strategy.generate_signals(m1_threshold=threshold)
            test_results = test_strategy.calculate_performance()

            if test_results:
                print(f"阈值{threshold:4.1f}%: 年化收益{test_results['strategy_annual_return']:6.2%}, "
                      f"最大回撤{test_results['strategy_max_drawdown']:6.2%}, "
                      f"夏普比率{test_results['strategy_sharpe']:5.3f}")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析过程出错: {e}")

if __name__ == "__main__":
    main()
