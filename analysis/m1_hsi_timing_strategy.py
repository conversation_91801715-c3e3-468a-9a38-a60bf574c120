#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国M1增速对恒生指数择时策略分析
使用中国M1货币供应量同比增长率作为择时信号来交易恒生指数
"""

import akshare as ak
import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams
import sqlite3
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

class M1HSITimingStrategy:
    def __init__(self, start_date='2015-01-01'):
        self.start_date = start_date
        self.m1_data = None
        self.hsi_data = None
        self.combined_data = None
        self.strategy_results = None
        
    def get_china_m1_data(self):
        """获取中国M1货币供应量数据"""
        print("🌐 正在获取中国M1货币供应量数据...")
        
        try:
            # 获取货币供应量数据
            money_supply_df = ak.macro_china_money_supply()
            
            if money_supply_df.empty:
                print("❌ 未获取到M1数据")
                return False
            
            # 数据清理和格式化
            money_supply_df['月份'] = money_supply_df['月份'].str.replace('年', '-').str.replace('月份', '').str.replace('月', '')
            money_supply_df['月份'] = pd.to_datetime(money_supply_df['月份'], format='%Y-%m')
            money_supply_df = money_supply_df.sort_values('月份')
            
            # 筛选需要的列
            m1_df = money_supply_df[['月份', '货币(M1)-数量(亿元)', '货币(M1)-同比增长']].copy()
            m1_df.columns = ['date', 'M1_amount', 'M1_growth']
            m1_df.set_index('date', inplace=True)
            
            # 筛选时间范围
            start_dt = pd.to_datetime(self.start_date)
            m1_df = m1_df[m1_df.index >= start_dt]
            
            self.m1_data = m1_df
            print(f"✅ 成功获取M1数据，共 {len(m1_df)} 条记录")
            print(f"📅 数据范围: {m1_df.index.min().strftime('%Y-%m')} 至 {m1_df.index.max().strftime('%Y-%m')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取M1数据失败: {e}")
            return False
    
    def get_hsi_data(self):
        """获取恒生指数数据"""
        print("🌐 正在使用yfinance获取恒生指数数据...")

        try:
            # 使用yfinance获取恒生指数数据
            # ^HSI 是恒生指数的Yahoo Finance代码
            hsi_ticker = yf.Ticker("^HSI")

            # 获取历史数据
            start_dt = pd.to_datetime(self.start_date)
            end_dt = pd.Timestamp.now()

            hsi_df = hsi_ticker.history(start=start_dt, end=end_dt)

            if hsi_df.empty:
                print("❌ 未获取到恒生指数数据")
                return False

            # 数据清理
            hsi_df = hsi_df[['Close']].copy()
            hsi_df.columns = ['close']

            # 处理时区问题
            if hsi_df.index.tz is not None:
                hsi_df.index = hsi_df.index.tz_convert(None)  # 转换为无时区
            hsi_df.index = pd.to_datetime(hsi_df.index)

            # 删除缺失值
            hsi_df = hsi_df.dropna()

            # 筛选时间范围
            hsi_df = hsi_df[hsi_df.index >= start_dt]

            self.hsi_data = hsi_df
            print(f"✅ 成功获取恒生指数数据，共 {len(hsi_df)} 条记录")
            print(f"📅 数据范围: {hsi_df.index.min().strftime('%Y-%m-%d')} 至 {hsi_df.index.max().strftime('%Y-%m-%d')}")

            return True

        except Exception as e:
            print(f"❌ 获取恒生指数数据失败: {e}")
            return False
    
    def combine_data(self):
        """合并M1和恒生指数数据，考虑数据发布时间滞后"""
        if self.m1_data is None or self.hsi_data is None:
            print("❌ 请先获取M1和恒生指数数据")
            return False

        print("🔄 正在合并数据（考虑每月15号发布上月数据的滞后）...")

        try:
            # 创建M1数据的发布时间表
            # 每月15号发布上个月的数据
            m1_publish_data = []

            for date, row in self.m1_data.iterrows():
                # 数据对应的月份
                data_month = date
                # 发布日期：下个月的15号
                if data_month.month == 12:
                    publish_date = pd.Timestamp(data_month.year + 1, 1, 15)
                else:
                    publish_date = pd.Timestamp(data_month.year, data_month.month + 1, 15)

                m1_publish_data.append({
                    'publish_date': publish_date,
                    'data_month': data_month,
                    'M1_amount': row['M1_amount'],
                    'M1_growth': row['M1_growth']
                })

            # 创建发布时间表DataFrame
            publish_df = pd.DataFrame(m1_publish_data)
            publish_df.set_index('publish_date', inplace=True)

            # 将发布数据扩展到日度（从发布日开始生效）
            daily_index = pd.date_range(start=self.hsi_data.index.min(),
                                      end=self.hsi_data.index.max(),
                                      freq='D')

            # 重新索引到日度，使用前向填充
            m1_daily = publish_df.reindex(daily_index, method='ffill')

            # 合并数据
            combined_df = pd.merge(self.hsi_data, m1_daily,
                                 left_index=True, right_index=True, how='inner')

            # 计算恒生指数收益率
            combined_df['hsi_return'] = combined_df['close'].pct_change()
            combined_df['hsi_return'] = combined_df['hsi_return'].fillna(0)
            combined_df['hsi_cumret'] = (1 + combined_df['hsi_return']).cumprod()

            # 删除没有M1数据的行
            combined_df = combined_df.dropna(subset=['M1_growth'])

            self.combined_data = combined_df
            print(f"✅ 数据合并完成，共 {len(combined_df)} 条记录")
            print(f"📅 考虑发布滞后后的数据范围: {combined_df.index.min().strftime('%Y-%m-%d')} 至 {combined_df.index.max().strftime('%Y-%m-%d')}")

            return True

        except Exception as e:
            print(f"❌ 数据合并失败: {e}")
            return False
    
    def generate_signals(self):
        """
        生成择时信号
        每月15号公布上月M1数据，M1增长率环比变化≥0时持有恒生指数，<0时持有现金
        """
        if self.combined_data is None:
            print("❌ 请先合并数据")
            return False

        print(f"🎯 生成择时信号 (基于M1增长率环比变化≥0)...")

        try:
            df = self.combined_data.copy()

            # 计算M1增长率的环比变化（基于数据月份）
            # 由于数据已经按发布时间对齐，直接计算变化
            df['M1_growth_change'] = df['M1_growth'].diff()

            # 生成信号：M1增长率环比变化≥0时做多，<0时持现
            df['signal'] = np.where(df['M1_growth_change'] >= 0, 1, 0)  # 1=做多, 0=持有现金

            # 处理第一个数据点（没有前值比较）
            # 假设第一个月的信号为1（做多）
            df['signal'].iloc[0] = 1

            df['signal_change'] = df['signal'].diff()

            # 计算策略收益
            # 使用当日信号（因为信号在15号就已知）
            df['strategy_return'] = df['signal'] * df['hsi_return']
            # 将NaN值填充为0
            df['strategy_return'] = df['strategy_return'].fillna(0)
            # 计算累计收益率（连续复利）
            df['strategy_cumret'] = (1 + df['strategy_return']).cumprod()

            # 计算基准收益（买入持有）
            df['benchmark_cumret'] = df['hsi_cumret']

            self.combined_data = df

            # 统计信号
            signal_stats = df['signal'].value_counts()
            total_days = len(df)
            long_days = signal_stats.get(1, 0)
            cash_days = signal_stats.get(0, 0)

            print(f"📊 信号统计:")
            print(f"   总交易日: {total_days}")
            print(f"   做多天数: {long_days} ({long_days/total_days*100:.1f}%)")
            print(f"   持现天数: {cash_days} ({cash_days/total_days*100:.1f}%)")

            # 显示M1增长率变化统计
            valid_changes = df['M1_growth_change'].dropna()
            positive_changes = len(valid_changes[valid_changes >= 0])
            negative_changes = len(valid_changes[valid_changes < 0])

            print(f"📈 M1增长率环比变化统计:")
            print(f"   环比上升或持平(≥0): {positive_changes} 次")
            print(f"   环比下降(<0): {negative_changes} 次")

            # 显示信号变化的时间点
            signal_changes = df[df['signal_change'] != 0].copy()
            if len(signal_changes) > 0:
                print(f"\n🔄 信号变化时间点 (前{min(10, len(signal_changes))}次):")
                for i, (date, row) in enumerate(signal_changes.head(10).iterrows()):
                    action = "做多" if row['signal'] == 1 else "持现"
                    m1_change = row['M1_growth_change']
                    print(f"   {date.strftime('%Y-%m-%d')}: {action} (M1变化: {m1_change:+.2f}%)")

            return True

        except Exception as e:
            print(f"❌ 信号生成失败: {e}")
            return False
    
    def calculate_performance(self):
        """计算策略表现"""
        if self.combined_data is None:
            print("❌ 请先生成信号")
            return None
        
        print("📊 计算策略表现...")
        
        try:
            df = self.combined_data.dropna()
            
            # 策略表现
            strategy_total_return = df['strategy_cumret'].iloc[-1] - 1
            benchmark_total_return = df['benchmark_cumret'].iloc[-1] - 1
            
            # 年化收益率
            years = (df.index[-1] - df.index[0]).days / 365.25
            strategy_annual_return = (df['strategy_cumret'].iloc[-1] ** (1/years)) - 1
            benchmark_annual_return = (df['benchmark_cumret'].iloc[-1] ** (1/years)) - 1
            
            # 波动率
            strategy_volatility = df['strategy_return'].std() * np.sqrt(252)
            benchmark_volatility = df['hsi_return'].std() * np.sqrt(252)
            
            # 夏普比率（假设无风险利率为0）
            strategy_sharpe = strategy_annual_return / strategy_volatility if strategy_volatility > 0 else 0
            benchmark_sharpe = benchmark_annual_return / benchmark_volatility if benchmark_volatility > 0 else 0
            
            # 最大回撤
            strategy_peak = df['strategy_cumret'].expanding().max()
            strategy_drawdown = (df['strategy_cumret'] - strategy_peak) / strategy_peak
            strategy_max_drawdown = strategy_drawdown.min()
            
            benchmark_peak = df['benchmark_cumret'].expanding().max()
            benchmark_drawdown = (df['benchmark_cumret'] - benchmark_peak) / benchmark_peak
            benchmark_max_drawdown = benchmark_drawdown.min()
            
            # 胜率
            positive_returns = df[df['strategy_return'] > 0]
            win_rate = len(positive_returns) / len(df[df['strategy_return'] != 0]) if len(df[df['strategy_return'] != 0]) > 0 else 0
            
            results = {
                'strategy_total_return': strategy_total_return,
                'benchmark_total_return': benchmark_total_return,
                'strategy_annual_return': strategy_annual_return,
                'benchmark_annual_return': benchmark_annual_return,
                'strategy_volatility': strategy_volatility,
                'benchmark_volatility': benchmark_volatility,
                'strategy_sharpe': strategy_sharpe,
                'benchmark_sharpe': benchmark_sharpe,
                'strategy_max_drawdown': strategy_max_drawdown,
                'benchmark_max_drawdown': benchmark_max_drawdown,
                'win_rate': win_rate,
                'years': years
            }
            
            self.strategy_results = results
            
            # 打印结果
            print("\n" + "="*60)
            print("📈 策略表现分析")
            print("="*60)
            print(f"📅 回测期间: {df.index[0].strftime('%Y-%m-%d')} 至 {df.index[-1].strftime('%Y-%m-%d')} ({years:.1f}年)")
            print(f"\n💰 收益表现:")
            print(f"   M1择时策略总收益: {strategy_total_return:.2%}")
            print(f"   买入持有总收益:   {benchmark_total_return:.2%}")
            print(f"   超额收益:         {strategy_total_return - benchmark_total_return:.2%}")
            print(f"\n📊 年化指标:")
            print(f"   M1择时策略年化收益: {strategy_annual_return:.2%}")
            print(f"   买入持有年化收益:   {benchmark_annual_return:.2%}")
            print(f"   M1择时策略波动率:   {strategy_volatility:.2%}")
            print(f"   买入持有波动率:     {benchmark_volatility:.2%}")
            print(f"   M1择时策略夏普比率: {strategy_sharpe:.3f}")
            print(f"   买入持有夏普比率:   {benchmark_sharpe:.3f}")
            print(f"\n⚠️  风险指标:")
            print(f"   M1择时策略最大回撤: {strategy_max_drawdown:.2%}")
            print(f"   买入持有最大回撤:   {benchmark_max_drawdown:.2%}")
            print(f"   策略胜率:           {win_rate:.2%}")
            
            return results

        except Exception as e:
            print(f"❌ 表现计算失败: {e}")
            return None

    def plot_results(self):
        """绘制策略结果图表"""
        if self.combined_data is None:
            print("❌ 没有数据可供绘图")
            return

        try:
            df = self.combined_data.dropna()

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('M1增速恒生指数择时策略分析', fontsize=16, fontweight='bold')

            # 1. M1增长率趋势及其变化
            ax1.plot(df.index, df['M1_growth'], color='blue', linewidth=2, label='M1同比增长率')
            ax1_twin = ax1.twinx()
            ax1_twin.plot(df.index, df['M1_growth_change'], color='orange', linewidth=1.5, alpha=0.7, label='M1增长率环比变化')
            ax1_twin.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='零线')
            ax1.set_title('中国M1货币供应量增长率及其变化', fontweight='bold')
            ax1.set_ylabel('M1同比增长率(%)', color='blue')
            ax1_twin.set_ylabel('M1增长率环比变化(%)', color='orange')
            ax1.legend(loc='upper left')
            ax1_twin.legend(loc='upper right')
            ax1.grid(True, alpha=0.3)

            # 2. 恒生指数价格
            ax2.plot(df.index, df['close'], color='green', linewidth=1.5, label='恒生指数')
            ax2.set_title('恒生指数价格走势', fontweight='bold')
            ax2.set_ylabel('指数点位')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 3. 累计收益对比
            ax3.plot(df.index, (df['strategy_cumret'] - 1) * 100,
                    color='red', linewidth=2, label='M1择时策略')
            ax3.plot(df.index, (df['benchmark_cumret'] - 1) * 100,
                    color='blue', linewidth=2, label='买入持有')
            ax3.set_title('累计收益率对比', fontweight='bold')
            ax3.set_ylabel('累计收益率(%)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 4. 信号分布
            signal_periods = []
            current_signal = None
            start_date = None

            for date, signal in df['signal'].items():
                if signal != current_signal:
                    if current_signal is not None:
                        signal_periods.append({
                            'start': start_date,
                            'end': date,
                            'signal': current_signal
                        })
                    current_signal = signal
                    start_date = date

            # 添加最后一个周期
            if current_signal is not None:
                signal_periods.append({
                    'start': start_date,
                    'end': df.index[-1],
                    'signal': current_signal
                })

            # 绘制信号区域
            for period in signal_periods:
                color = 'lightgreen' if period['signal'] == 1 else 'lightcoral'
                label = '做多期' if period['signal'] == 1 else '持现期'
                ax4.axvspan(period['start'], period['end'], alpha=0.3, color=color)

            ax4.plot(df.index, df['M1_growth_change'], color='purple', linewidth=2, label='M1增长率环比变化')
            ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='零线')
            ax4.set_title('择时信号分布（基于M1增长率环比变化）', fontweight='bold')
            ax4.set_ylabel('M1增长率环比变化(%)')
            ax4.legend()

            # 添加图例
            from matplotlib.patches import Patch
            legend_elements = [Patch(facecolor='lightgreen', alpha=0.3, label='做多期'),
                             Patch(facecolor='lightcoral', alpha=0.3, label='持现期')]
            ax4.legend(handles=legend_elements, loc='upper right')
            ax4.grid(True, alpha=0.3)

            # 调整布局
            plt.tight_layout()

            # 保存图表
            chart_filename = f'm1_hsi_timing_strategy_{datetime.now().strftime("%Y%m%d")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存到 {chart_filename}")

            # 显示图表
            plt.show()

        except Exception as e:
            print(f"❌ 绘制图表失败: {e}")

    def save_results(self):
        """保存结果到文件"""
        if self.combined_data is None or self.strategy_results is None:
            print("❌ 没有结果可供保存")
            return

        try:
            # 保存详细数据到CSV
            csv_filename = f'm1_hsi_timing_data_{datetime.now().strftime("%Y%m%d")}.csv'
            self.combined_data.to_csv(csv_filename, encoding='utf-8-sig')
            print(f"💾 详细数据已保存到 {csv_filename}")

            # 保存结果到SQLite数据库
            db_filename = 'timing_strategy_results.db'
            with sqlite3.connect(db_filename) as conn:
                # 保存策略数据
                self.combined_data.reset_index().to_sql('m1_hsi_timing', conn,
                                                       if_exists='replace', index=False)

                # 保存策略表现
                results_df = pd.DataFrame([self.strategy_results])
                results_df['strategy_name'] = 'M1_HSI_Timing'
                results_df['update_time'] = datetime.now()
                results_df.to_sql('strategy_performance', conn,
                                if_exists='append', index=False)

            print(f"💾 结果已保存到数据库 {db_filename}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    print("🚀 M1增速恒生指数择时策略分析")
    print("=" * 60)

    # 创建策略实例
    strategy = M1HSITimingStrategy(start_date='2015-01-01')

    try:
        # 1. 获取M1数据
        if not strategy.get_china_m1_data():
            return

        # 2. 获取恒生指数数据
        if not strategy.get_hsi_data():
            return

        # 3. 合并数据
        if not strategy.combine_data():
            return

        # 4. 生成择时信号
        if not strategy.generate_signals():
            return

        # 5. 计算策略表现
        results = strategy.calculate_performance()
        if results is None:
            return

        # 6. 绘制结果图表
        strategy.plot_results()

        # 7. 保存结果
        strategy.save_results()

        print("\n✅ M1环比变化择时策略分析完成!")
        print("\n💡 策略说明:")
        print("   📅 每月15号公布上月M1数据")
        print("   📈 M1增长率环比变化≥0时 → 做多恒生指数")
        print("   📉 M1增长率环比变化<0时 → 持有现金")
        print("   ⏰ 考虑数据发布时间滞后效应")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析过程出错: {e}")

if __name__ == "__main__":
    main()
