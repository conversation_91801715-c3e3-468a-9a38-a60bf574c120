#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试M1环比变化择时策略（考虑数据发布滞后）
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
from datetime import datetime, timedelta

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def create_test_data():
    """创建测试数据"""
    
    # 创建2020-2023年的测试数据
    start_date = pd.Timestamp('2020-01-01')
    end_date = pd.Timestamp('2023-12-31')
    
    # 恒生指数日度数据
    dates = pd.date_range(start_date, end_date, freq='D')
    np.random.seed(42)
    
    # 模拟恒生指数收益率
    returns = np.random.normal(0.0003, 0.015, len(dates))
    prices = 25000 * (1 + returns).cumprod()
    
    hsi_df = pd.DataFrame({
        'close': prices,
        'hsi_return': returns
    }, index=dates)
    
    # M1月度数据（模拟真实的M1增长率变化）
    monthly_dates = pd.date_range('2020-01-01', '2023-12-01', freq='MS')
    
    # 模拟M1增长率数据（有一定的趋势和波动）
    base_growth = 8.0
    trend = np.linspace(-1, 2, len(monthly_dates))  # 整体上升趋势
    noise = np.random.normal(0, 0.8, len(monthly_dates))  # 随机波动
    m1_growth = base_growth + trend + noise
    
    m1_df = pd.DataFrame({
        'M1_amount': np.random.uniform(300000, 400000, len(monthly_dates)),
        'M1_growth': m1_growth
    }, index=monthly_dates)
    
    return hsi_df, m1_df

def apply_publication_lag(m1_df):
    """应用数据发布滞后（每月15号发布上月数据）"""
    
    publish_data = []
    
    for date, row in m1_df.iterrows():
        # 数据对应的月份
        data_month = date
        # 发布日期：下个月的15号
        if data_month.month == 12:
            publish_date = pd.Timestamp(data_month.year + 1, 1, 15)
        else:
            publish_date = pd.Timestamp(data_month.year, data_month.month + 1, 15)
        
        publish_data.append({
            'publish_date': publish_date,
            'data_month': data_month,
            'M1_amount': row['M1_amount'],
            'M1_growth': row['M1_growth']
        })
    
    publish_df = pd.DataFrame(publish_data)
    publish_df.set_index('publish_date', inplace=True)
    
    return publish_df

def generate_signals(combined_df):
    """生成择时信号"""
    
    df = combined_df.copy()
    
    # 计算M1增长率环比变化
    df['M1_growth_change'] = df['M1_growth'].diff()
    
    # 生成信号：M1增长率环比变化≥0时做多，<0时持现
    df['signal'] = np.where(df['M1_growth_change'] >= 0, 1, 0)
    
    # 处理第一个数据点
    df['signal'].iloc[0] = 1
    
    # 计算策略收益
    df['strategy_return'] = df['signal'] * df['hsi_return']
    df['strategy_return'] = df['strategy_return'].fillna(0)
    df['strategy_cumret'] = (1 + df['strategy_return']).cumprod()
    df['benchmark_cumret'] = (1 + df['hsi_return']).cumprod()
    
    return df

def analyze_strategy():
    """分析策略表现"""
    
    print("🧪 测试M1环比变化择时策略（考虑发布滞后）")
    print("=" * 60)
    
    # 1. 创建测试数据
    hsi_df, m1_df = create_test_data()
    print(f"📊 恒生指数数据: {len(hsi_df)} 条记录")
    print(f"📊 M1数据: {len(m1_df)} 条记录")
    
    # 2. 应用发布滞后
    publish_df = apply_publication_lag(m1_df)
    print(f"📅 考虑发布滞后后的M1数据: {len(publish_df)} 条记录")
    
    # 3. 合并数据
    daily_index = hsi_df.index
    m1_daily = publish_df.reindex(daily_index, method='ffill')
    combined_df = pd.merge(hsi_df, m1_daily, left_index=True, right_index=True, how='inner')
    combined_df = combined_df.dropna(subset=['M1_growth'])
    
    print(f"📊 合并后数据: {len(combined_df)} 条记录")
    print(f"📅 数据范围: {combined_df.index.min().strftime('%Y-%m-%d')} 至 {combined_df.index.max().strftime('%Y-%m-%d')}")
    
    # 4. 生成信号
    result_df = generate_signals(combined_df)
    
    # 5. 计算表现
    strategy_total_return = result_df['strategy_cumret'].iloc[-1] - 1
    benchmark_total_return = result_df['benchmark_cumret'].iloc[-1] - 1
    
    years = (result_df.index[-1] - result_df.index[0]).days / 365.25
    strategy_annual_return = (result_df['strategy_cumret'].iloc[-1] ** (1/years)) - 1
    benchmark_annual_return = (result_df['benchmark_cumret'].iloc[-1] ** (1/years)) - 1
    
    strategy_volatility = result_df['strategy_return'].std() * np.sqrt(252)
    benchmark_volatility = result_df['hsi_return'].std() * np.sqrt(252)
    
    strategy_sharpe = strategy_annual_return / strategy_volatility if strategy_volatility > 0 else 0
    benchmark_sharpe = benchmark_annual_return / benchmark_volatility if benchmark_volatility > 0 else 0
    
    # 6. 打印结果
    print("\n" + "="*60)
    print("📈 策略表现分析")
    print("="*60)
    print(f"📅 回测期间: {result_df.index[0].strftime('%Y-%m-%d')} 至 {result_df.index[-1].strftime('%Y-%m-%d')} ({years:.1f}年)")
    print(f"\n💰 收益表现:")
    print(f"   M1择时策略总收益: {strategy_total_return:.2%}")
    print(f"   买入持有总收益:   {benchmark_total_return:.2%}")
    print(f"   超额收益:         {strategy_total_return - benchmark_total_return:.2%}")
    print(f"\n📊 年化指标:")
    print(f"   M1择时策略年化收益: {strategy_annual_return:.2%}")
    print(f"   买入持有年化收益:   {benchmark_annual_return:.2%}")
    print(f"   M1择时策略波动率:   {strategy_volatility:.2%}")
    print(f"   买入持有波动率:     {benchmark_volatility:.2%}")
    print(f"   M1择时策略夏普比率: {strategy_sharpe:.3f}")
    print(f"   买入持有夏普比率:   {benchmark_sharpe:.3f}")
    
    # 7. 信号统计
    signal_stats = result_df['signal'].value_counts()
    total_days = len(result_df)
    long_days = signal_stats.get(1, 0)
    cash_days = signal_stats.get(0, 0)
    
    print(f"\n📊 信号统计:")
    print(f"   总交易日: {total_days}")
    print(f"   做多天数: {long_days} ({long_days/total_days*100:.1f}%)")
    print(f"   持现天数: {cash_days} ({cash_days/total_days*100:.1f}%)")
    
    # 8. M1变化统计
    valid_changes = result_df['M1_growth_change'].dropna()
    positive_changes = len(valid_changes[valid_changes >= 0])
    negative_changes = len(valid_changes[valid_changes < 0])
    
    print(f"\n📈 M1增长率环比变化统计:")
    print(f"   环比上升或持平(≥0): {positive_changes} 次")
    print(f"   环比下降(<0): {negative_changes} 次")
    
    # 9. 绘制图表
    plot_results(result_df)
    
    return result_df

def plot_results(df):
    """绘制结果图表"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('M1环比变化择时策略分析（考虑发布滞后）', fontsize=16, fontweight='bold')
    
    # 1. M1增长率及其变化
    ax1.plot(df.index, df['M1_growth'], 'b-', linewidth=2, label='M1增长率')
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df.index, df['M1_growth_change'], 'r-', linewidth=1.5, alpha=0.7, label='M1增长率变化')
    ax1_twin.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax1.set_title('M1增长率及其环比变化')
    ax1.set_ylabel('M1增长率(%)', color='blue')
    ax1_twin.set_ylabel('环比变化(%)', color='red')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 恒生指数价格
    ax2.plot(df.index, df['close'], 'g-', linewidth=1.5)
    ax2.set_title('恒生指数价格')
    ax2.set_ylabel('价格')
    ax2.grid(True, alpha=0.3)
    
    # 3. 累计收益率对比
    ax3.plot(df.index, (df['strategy_cumret'] - 1) * 100, 'r-', linewidth=2, label='M1择时策略')
    ax3.plot(df.index, (df['benchmark_cumret'] - 1) * 100, 'b-', linewidth=2, label='买入持有')
    ax3.set_title('累计收益率对比')
    ax3.set_ylabel('累计收益率(%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 信号分布
    signal_periods = []
    current_signal = None
    start_date = None
    
    for date, signal in df['signal'].items():
        if signal != current_signal:
            if current_signal is not None:
                signal_periods.append({
                    'start': start_date,
                    'end': date,
                    'signal': current_signal
                })
            current_signal = signal
            start_date = date
    
    # 添加最后一个周期
    if current_signal is not None:
        signal_periods.append({
            'start': start_date,
            'end': df.index[-1],
            'signal': current_signal
        })
    
    # 绘制信号区域
    for period in signal_periods:
        color = 'lightgreen' if period['signal'] == 1 else 'lightcoral'
        ax4.axvspan(period['start'], period['end'], alpha=0.3, color=color)
    
    ax4.plot(df.index, df['M1_growth_change'], 'purple', linewidth=2)
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax4.set_title('择时信号分布')
    ax4.set_ylabel('M1增长率变化(%)')
    ax4.grid(True, alpha=0.3)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='lightgreen', alpha=0.3, label='做多期'),
                      Patch(facecolor='lightcoral', alpha=0.3, label='持现期')]
    ax4.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.savefig('m1_timing_strategy_with_lag.png', dpi=300, bbox_inches='tight')
    print(f"\n📊 图表已保存到 m1_timing_strategy_with_lag.png")
    plt.show()

if __name__ == "__main__":
    result_df = analyze_strategy()
    
    print("\n✅ 测试完成!")
    print("\n💡 策略说明:")
    print("   📅 每月15号公布上月M1数据")
    print("   📈 M1增长率环比变化≥0时 → 做多恒生指数")
    print("   📉 M1增长率环比变化<0时 → 持有现金")
    print("   ⏰ 考虑数据发布时间滞后效应")
